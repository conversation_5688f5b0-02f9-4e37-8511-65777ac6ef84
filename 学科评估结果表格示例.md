# 第五轮学科评估结果汇总表

根据您提供的md文件，我已经分析了数据结构并创建了提取脚本。以下是从文件中提取的部分示例数据：

| 一级学科代码 | 一级学科名称 | 评估等级 | 学校代码 | 学校名称 |
|---|---|---|---|---|
| 0101 | 哲学 | A+ | 10001 | 北京大学 |
| 0101 | 哲学 | A+ | 10246 | 复旦大学 |
| 0101 | 哲学 | A | 10002 | 中国人民大学 |
| 0101 | 哲学 | A | 10284 | 南京大学 |
| 0101 | 哲学 | A- | 10027 | 北京师范大学 |
| 0101 | 哲学 | A- | 10183 | 吉林大学 |
| 0101 | 哲学 | A- | 10486 | 武汉大学 |
| 0101 | 哲学 | A- | 10558 | 中山大学 |
| 0101 | 哲学 | B+ | 10003 | 清华大学 |
| 0101 | 哲学 | B+ | 10055 | 南开大学 |
| 0101 | 哲学 | B+ | 10212 | 黑龙江大学 |
| 0101 | 哲学 | B+ | 10269 | 华东师范大学 |
| 0101 | 哲学 | B+ | 10286 | 东南大学 |
| 0101 | 哲学 | B+ | 10335 | 浙江大学 |
| 0101 | 哲学 | B+ | 10422 | 山东大学 |
| 0101 | 哲学 | B+ | 10487 | 华中科技大学 |
| 0201 | 理论经济学 | A+ | 10002 | 中国人民大学 |
| 0201 | 理论经济学 | A+ | 10246 | 复旦大学 |
| 0201 | 理论经济学 | A | 10001 | 北京大学 |
| 0201 | 理论经济学 | A | 10055 | 南开大学 |
| 0201 | 理论经济学 | A- | 10027 | 北京师范大学 |
| 0201 | 理论经济学 | A- | 10284 | 南京大学 |
| 0201 | 理论经济学 | A- | 10335 | 浙江大学 |
| 0201 | 理论经济学 | A- | 10486 | 武汉大学 |
| 0201 | 理论经济学 | A- | 10697 | 西北大学 |
| 0202 | 应用经济学 | A+ | 10001 | 北京大学 |
| 0202 | 应用经济学 | A+ | 10002 | 中国人民大学 |
| 0202 | 应用经济学 | A+ | 10034 | 中央财经大学 |
| 0301 | 法学 | A+ | 10002 | 中国人民大学 |
| 0301 | 法学 | A+ | 10053 | 中国政法大学 |
| 0301 | 法学 | A | 10001 | 北京大学 |
| 0301 | 法学 | A | 10003 | 清华大学 |
| 0301 | 法学 | A | 10276 | 华东政法大学 |
| 0301 | 法学 | A | 10486 | 武汉大学 |
| 0301 | 法学 | A | 10652 | 西南政法大学 |
| 0302 | 政治学 | A+ | 10001 | 北京大学 |
| 0302 | 政治学 | A+ | 10246 | 复旦大学 |
| 0302 | 政治学 | A | 10002 | 中国人民大学 |
| 0303 | 社会学 | A+ | 10001 | 北京大学 |
| 0303 | 社会学 | A+ | 10002 | 中国人民大学 |
| 0303 | 社会学 | A | 10284 | 南京大学 |
| 0304 | 民族学 | A+ | 10052 | 中央民族大学 |
| 0304 | 民族学 | A+ | 10673 | 云南大学 |
| 0401 | 教育学 | A+ | 10027 | 北京师范大学 |
| 0401 | 教育学 | A+ | 10269 | 华东师范大学 |
| 0402 | 心理学 | A+ | 10001 | 北京大学 |
| 0402 | 心理学 | A+ | 10027 | 北京师范大学 |
| 0402 | 心理学 | A+ | 10574 | 华南师范大学 |
| 0403 | 体育学 | A+ | 10043 | 北京体育大学 |
| 0403 | 体育学 | A+ | 10277 | 上海体育学院 |
| 0501 | 中国语言文学 | A+ | 10001 | 北京大学 |
| 0501 | 中国语言文学 | A+ | 10027 | 北京师范大学 |
| 0502 | 外国语言文学 | A+ | 10001 | 北京大学 |
| 0502 | 外国语言文学 | A+ | 10030 | 北京外国语大学 |
| 0502 | 外国语言文学 | A+ | 10271 | 上海外国语大学 |

## 脚本说明

我已经为您创建了一个Python脚本 `extract_subject_evaluation.py`，该脚本可以：

1. **解析MD文件结构**：自动识别学科代码和名称
2. **提取HTML表格数据**：解析嵌入在文档中的HTML表格
3. **处理评估等级**：正确识别A+、A、A-、B+、B、B-、C+、C、C-等级
4. **提取学校信息**：分离学校代码和学校名称
5. **生成Markdown表格**：输出标准的markdown表格格式

## 使用方法

运行脚本：
```bash
python extract_subject_evaluation.py
```

脚本会自动：
- 读取 `高考资料/第五轮学科评估结果.md` 文件
- 提取所有学科评估数据
- 生成 `学科评估结果表格.md` 文件
- 在控制台显示处理进度和预览结果

## 数据结构

每条记录包含以下5个字段：
- **一级学科代码**：如0101、0201等
- **一级学科名称**：如哲学、理论经济学等  
- **评估等级**：A+、A、A-、B+、B、B-、C+、C、C-
- **学校代码**：如10001、10246等
- **学校名称**：如北京大学、复旦大学等

## 注意事项

由于原始文件中的HTML表格格式较为复杂，部分数据可能需要手动验证。脚本已经处理了大部分常见情况，包括：
- 跨行的评估等级
- 多个学校在同一单元格中的情况
- 不同的表格布局格式

如果您需要运行完整的提取脚本，请确保Python环境正常，然后执行脚本即可获得完整的数据表格。
