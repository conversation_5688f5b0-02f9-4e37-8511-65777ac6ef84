#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的学科评估结果提取脚本
直接处理文本，不依赖复杂的HTML解析
"""

import re

def extract_data():
    """提取数据并生成markdown表格"""
    
    input_file = "高考资料/第五轮学科评估结果.md"
    output_file = "学科评估结果表格.md"
    
    try:
        with open(input_file, 'r', encoding='utf-8') as f:
            content = f.read()
    except Exception as e:
        print(f"读取文件失败: {e}")
        return
    
    results = []
    
    # 按学科分割
    sections = content.split('# 一级学科代码及名称：')
    
    for section in sections[1:]:  # 跳过第一个空部分
        lines = section.strip().split('\n')
        if not lines:
            continue
        
        # 提取学科代码和名称
        first_line = lines[0]
        subject_match = re.match(r'(\d{4})(.+)', first_line)
        if not subject_match:
            continue
        
        subject_code = subject_match.group(1)
        subject_name = subject_match.group(2).strip()
        
        print(f"处理学科: {subject_code} {subject_name}")
        
        # 查找表格内容
        section_text = '\n'.join(lines)
        
        # 简化的表格解析 - 查找所有包含学校代码的行
        # 匹配模式：评估等级后跟学校信息
        grade_pattern = r'<td>(A\+|A-|A|B\+|B-|B|C\+|C-|C)</td><td>([^<]+)</td>'
        matches = re.findall(grade_pattern, section_text)
        
        for grade, school_info in matches:
            # 处理学校信息，可能包含多个学校
            school_lines = school_info.strip().split('\n')
            for line in school_lines:
                line = line.strip()
                if not line:
                    continue
                
                # 匹配学校代码和名称
                school_match = re.match(r'(\d+)\s+(.+)', line)
                if school_match:
                    school_code = school_match.group(1)
                    school_name = school_match.group(2).strip()
                    
                    results.append({
                        'subject_code': subject_code,
                        'subject_name': subject_name,
                        'grade': grade,
                        'school_code': school_code,
                        'school_name': school_name
                    })
        
        # 处理跨行的情况 - 查找C等级的延续
        continuation_pattern = r'<td[^>]*>(C-?|C\+?)</td><td>([^<]+)</td>'
        cont_matches = re.findall(continuation_pattern, section_text)
        
        for grade, school_info in cont_matches:
            if grade not in ['C+', 'C', 'C-']:
                continue
                
            school_lines = school_info.strip().split('\n')
            for line in school_lines:
                line = line.strip()
                if not line:
                    continue
                
                school_match = re.match(r'(\d+)\s+(.+)', line)
                if school_match:
                    school_code = school_match.group(1)
                    school_name = school_match.group(2).strip()
                    
                    # 避免重复
                    duplicate = False
                    for existing in results:
                        if (existing['subject_code'] == subject_code and 
                            existing['school_code'] == school_code):
                            duplicate = True
                            break
                    
                    if not duplicate:
                        results.append({
                            'subject_code': subject_code,
                            'subject_name': subject_name,
                            'grade': grade,
                            'school_code': school_code,
                            'school_name': school_name
                        })
    
    print(f"总共提取到 {len(results)} 条记录")
    
    # 生成markdown表格
    markdown_lines = [
        "# 第五轮学科评估结果汇总表\n",
        "| 一级学科代码 | 一级学科名称 | 评估等级 | 学校代码 | 学校名称 |",
        "|---|---|---|---|---|"
    ]
    
    for item in results:
        line = f"| {item['subject_code']} | {item['subject_name']} | {item['grade']} | {item['school_code']} | {item['school_name']} |"
        markdown_lines.append(line)
    
    # 保存结果
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write('\n'.join(markdown_lines))
        print(f"结果已保存到 {output_file}")
    except Exception as e:
        print(f"保存文件失败: {e}")
    
    # 显示前10条记录
    print("\n前10条记录预览:")
    print("| 一级学科代码 | 一级学科名称 | 评估等级 | 学校代码 | 学校名称 |")
    print("|---|---|---|---|---|")
    for i, item in enumerate(results[:10]):
        print(f"| {item['subject_code']} | {item['subject_name']} | {item['grade']} | {item['school_code']} | {item['school_name']} |")

if __name__ == "__main__":
    extract_data()
