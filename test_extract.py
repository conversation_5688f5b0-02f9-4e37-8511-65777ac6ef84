#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版学科评估结果提取脚本
"""

import re
import html

def test_parse():
    """测试解析功能"""
    
    # 测试数据 - 从原文件中提取的一小段
    test_content = """# 一级学科代码及名称：0101哲学

本一级学科中，全国具有"博士授权"的高校共48所，本次参评38所；部分具有"硕士授权"的高校也参加了评估；参评高校共计84所（注：评估结果相同的高校排序不分先后，按学校代码排列）。

<table><tr><td>评估结果</td><td>学校代码及名称</td></tr><tr><td>A+</td><td>10001 北京大学
10246 复旦大学</td></tr><tr><td>A</td><td>10002 中国人民大学
10284 南京大学</td></tr><tr><td>A-</td><td>10027 北京师范大学
10183 吉林大学
10486 武汉大学
10558 中山大学</td></tr></table>"""

    results = []
    
    # 提取学科信息
    subject_match = re.search(r'# 一级学科代码及名称：(\d{4})(.+)', test_content)
    if subject_match:
        subject_code = subject_match.group(1)
        subject_name = subject_match.group(2).strip()
        print(f"学科代码: {subject_code}")
        print(f"学科名称: {subject_name}")
    
    # 查找表格
    table_pattern = r'<table>.*?</table>'
    tables = re.findall(table_pattern, test_content, re.DOTALL)
    
    print(f"找到 {len(tables)} 个表格")
    
    for table in tables:
        print("解析表格...")
        
        # 解析表格行
        row_pattern = r'<tr>(.*?)</tr>'
        rows = re.findall(row_pattern, table, re.DOTALL)
        
        current_grade = None
        
        for row in rows:
            # 解析单元格
            cell_pattern = r'<td[^>]*>(.*?)</td>'
            cells = re.findall(cell_pattern, row, re.DOTALL)
            
            if len(cells) < 2:
                continue
                
            # 清理HTML内容
            cleaned_cells = []
            for cell in cells:
                clean_cell = re.sub(r'<[^>]+>', '', cell)
                clean_cell = html.unescape(clean_cell).strip()
                cleaned_cells.append(clean_cell)
            
            # 跳过表头
            if cleaned_cells[0] == '评估结果':
                continue
            
            # 处理评估等级
            if cleaned_cells[0] in ['A+', 'A', 'A-', 'B+', 'B', 'B-', 'C+', 'C', 'C-']:
                current_grade = cleaned_cells[0]
                school_info = cleaned_cells[1] if len(cleaned_cells) > 1 else ''
            else:
                school_info = cleaned_cells[0]
            
            if not current_grade or not school_info:
                continue
                
            print(f"等级: {current_grade}, 学校信息: {school_info}")
            
            # 解析学校信息
            school_lines = school_info.split('\n')
            for line in school_lines:
                line = line.strip()
                if not line:
                    continue
                    
                # 匹配学校代码和名称
                school_match = re.match(r'(\d+)\s+(.+)', line)
                if school_match:
                    school_code = school_match.group(1)
                    school_name = school_match.group(2).strip()
                    
                    result = {
                        'subject_code': subject_code,
                        'subject_name': subject_name,
                        'grade': current_grade,
                        'school_code': school_code,
                        'school_name': school_name
                    }
                    results.append(result)
                    print(f"  -> {school_code} {school_name}")
    
    print(f"\n总共提取到 {len(results)} 条记录")
    
    # 生成markdown表格
    if results:
        print("\nMarkdown表格:")
        print("| 一级学科代码 | 一级学科名称 | 评估等级 | 学校代码 | 学校名称 |")
        print("|---|---|---|---|---|")
        for item in results:
            print(f"| {item['subject_code']} | {item['subject_name']} | {item['grade']} | {item['school_code']} | {item['school_name']} |")

if __name__ == "__main__":
    test_parse()
